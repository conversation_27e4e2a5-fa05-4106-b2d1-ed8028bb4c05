import pyodbc as pdb

try:
    # Establish the connection
    conn = pdb.connect("dsn=BWH25;uid=marietjie;pwd=renier")
    print("Connection successful!")

    # Create a cursor object to execute queries
    cursor = conn.cursor()

    # Execute the query to select the top 10 rows from the LedgerMaster table
    cursor.execute("SELECT TOP 10 * FROM LedgerMaster")

    # Fetch all the rows returned by the query
    rows = cursor.fetchall()

    # Display the data
    for row in rows:
        print(row)

    # Close the cursor and connection
    cursor.close()
    conn.close()
    print("Connection closed.")
    
except pdb.Error as e:
    print("Connection failed:", e)