import sys
from PyQt5.QtWidgets import (QA<PERSON>lication, QMainWindow, QVBoxLayout, 
                             QWidget, QPushButton, QTableWidget, 
                             QTableWidgetItem, QLineEdit, QLabel,
                             QMessageBox, QHBoxLayout)
from PyQt5.QtCore import QThread, pyqtSignal
import pandas as pd
from db_bridge import DatabaseBridge

class DatabaseWorker(QThread):
    """Worker thread for database operations to prevent UI freezing"""
    data_ready = pyqtSignal(pd.DataFrame)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, query, params=None):
        super().__init__()
        self.query = query
        self.params = params
        
    def run(self):
        try:
            db = DatabaseBridge()
            df = db.query_to_dataframe(self.query, self.params)
            self.data_ready.emit(df)
        except Exception as e:
            self.error_occurred.emit(str(e))

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("BWH Price Tool - Database Viewer")
        self.setGeometry(100, 100, 800, 600)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout
        layout = QVBoxLayout(central_widget)
        
        # Search section
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("Search:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Enter search term...")
        search_layout.addWidget(self.search_input)
        
        self.search_button = QPushButton("Search Ledger")
        self.search_button.clicked.connect(self.search_ledger)
        search_layout.addWidget(self.search_button)
        
        layout.addLayout(search_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.load_button = QPushButton("Load Sample Data")
        self.load_button.clicked.connect(self.load_sample_data)
        button_layout.addWidget(self.load_button)
        
        self.test_button = QPushButton("Test Connection")
        self.test_button.clicked.connect(self.test_connection)
        button_layout.addWidget(self.test_button)
        
        layout.addLayout(button_layout)
        
        # Table
        self.table = QTableWidget()
        layout.addWidget(self.table)
        
        # Status label
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)
        
    def load_sample_data(self):
        """Load sample data from LedgerMaster"""
        self.status_label.setText("Loading data...")
        self.load_button.setEnabled(False)
        
        # Create worker thread
        self.worker = DatabaseWorker("SELECT TOP 20 * FROM LedgerMaster")
        self.worker.data_ready.connect(self.display_data)
        self.worker.error_occurred.connect(self.show_error)
        self.worker.finished.connect(lambda: self.load_button.setEnabled(True))
        self.worker.start()
    
    def search_ledger(self):
        """Search ledger based on input"""
        search_term = self.search_input.text().strip()
        if not search_term:
            QMessageBox.warning(self, "Warning", "Please enter a search term")
            return
            
        self.status_label.setText(f"Searching for '{search_term}'...")
        self.search_button.setEnabled(False)
        
        query = """
        SELECT * FROM LedgerMaster 
        WHERE LedgerName LIKE ? OR LedgerCode LIKE ?
        """
        params = [f"%{search_term}%", f"%{search_term}%"]
        
        self.worker = DatabaseWorker(query, params)
        self.worker.data_ready.connect(self.display_data)
        self.worker.error_occurred.connect(self.show_error)
        self.worker.finished.connect(lambda: self.search_button.setEnabled(True))
        self.worker.start()
    
    def test_connection(self):
        """Test database connection"""
        self.status_label.setText("Testing connection...")
        self.test_button.setEnabled(False)
        
        self.worker = DatabaseWorker("SELECT 1 as test")
        self.worker.data_ready.connect(lambda df: self.status_label.setText("✓ Connection successful!"))
        self.worker.error_occurred.connect(self.show_error)
        self.worker.finished.connect(lambda: self.test_button.setEnabled(True))
        self.worker.start()
    
    def display_data(self, df):
        """Display DataFrame in table widget"""
        if df.empty:
            self.status_label.setText("No data found")
            self.table.setRowCount(0)
            self.table.setColumnCount(0)
            return
            
        # Set up table
        self.table.setRowCount(len(df))
        self.table.setColumnCount(len(df.columns))
        self.table.setHorizontalHeaderLabels(df.columns.tolist())
        
        # Fill table with data
        for i, row in df.iterrows():
            for j, value in enumerate(row):
                item = QTableWidgetItem(str(value))
                self.table.setItem(i, j, item)
        
        # Resize columns to content
        self.table.resizeColumnsToContents()
        
        self.status_label.setText(f"Loaded {len(df)} rows")
    
    def show_error(self, error_message):
        """Show error message"""
        self.status_label.setText("Error occurred")
        QMessageBox.critical(self, "Database Error", error_message)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
