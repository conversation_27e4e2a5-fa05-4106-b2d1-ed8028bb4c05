import sys
import pyodbc
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QLabel,
                             QComboBox, QPushButton, QTableWidget, QTableWidgetItem,
                             QLineEdit, QMessageBox)

class SalesDataApp(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.connection = self.create_connection()

    def initUI(self):
        self.setWindowTitle("Sales Data Query")
        layout = QVBoxLayout()

        self.customer_combo = QComboBox()
        self.item_combo = QComboBox()
        self.new_gp_input = QLineEdit("Enter NEW GP%")
        self.new_gp_label = QLabel("NEW GP:")
        self.new_gp_display = QLineEdit()
        self.new_gp_display.setReadOnly(True)

        self.fetch_button = QPushButton("Fetch Sales Data")
        self.fetch_button.clicked.connect(self.fetch_data)

        self.table = QTableWidget()

        layout.addWidget(QLabel("Customer Code:"))
        layout.addWidget(self.customer_combo)
        layout.addWidget(QLabel("Item Code:"))
        layout.addWidget(self.item_combo)
        layout.addWidget(self.new_gp_input)
        layout.addWidget(self.new_gp_label)
        layout.addWidget(self.new_gp_display)
        layout.addWidget(self.fetch_button)
        layout.addWidget(self.table)

        self.setLayout(layout)

        self.populate_dropdowns()

    def create_connection(self):
        conn_string = 'DSN=BWH25;UID=your_username;PWD=your_password'
        return pyodbc.connect(conn_string)

    def populate_dropdowns(self):
        customers = pd.read_sql("SELECT DISTINCT CustomerCode FROM HistoryLines", self.connection)
        items = pd.read_sql("SELECT DISTINCT ItemCode FROM HistoryLines", self.connection)

        self.customer_combo.addItems(customers['CustomerCode'].tolist())
        self.item_combo.addItems(items['ItemCode'].tolist())

    def fetch_data(self):
        customer_code = self.customer_combo.currentText()
        item_code = self.item_combo.currentText()

        query = f"""
        SELECT TOP 10 CustomerCode, ItemCode, SalesmanCode, DDate,
               Description, CostPrice, Qty, UnitPrice 
        FROM HistoryLines 
        WHERE SearchType = '4' AND CustomerCode = ? AND ItemCode = ?
        ORDER BY DDate DESC
        """
        data = pd.read_sql(query, self.connection, params=(customer_code, item_code))

        if data.empty:
            QMessageBox.warning(self, "No Data", "No records found for the selected criteria.")
            return

        data['GP'] = data['UnitPrice'] - data['CostPrice']
        data['GP%'] = (data['GP'] / data['UnitPrice']) * 100

        self.new_gp_display.setText(f"{data['GP%'].mean():.2f}%")
        self.table.setRowCount(len(data))
        self.table.setColumnCount(len(data.columns))
        self.table.setHorizontalHeaderLabels(data.columns)

        for i in range(len(data)):
            for j in range(len(data.columns)):
                self.table.setItem(i, j, QTableWidgetItem(str(data.iat[i, j])))

    def closeEvent(self, event):
        self.connection.close()
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = SalesDataApp()
    window.resize(800, 600)
    window.show()
    sys.exit(app.exec_())