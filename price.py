import sys
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QComboBox, QPushButton, QTableWidget, QTableWidgetItem,
                             QLineEdit, QMessageBox)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from db_bridge import DatabaseBridge

def get_dark_stylesheet():
    """Return dark mode stylesheet for the application"""
    return """
    QWidget {
        background-color: #2b2b2b;
        color: #ffffff;
        font-family: 'Segoe UI', Arial, sans-serif;
        font-size: 9pt;
    }

    QMainWindow {
        background-color: #2b2b2b;
    }

    QLabel {
        color: #ffffff;
        background-color: transparent;
        padding: 2px;
    }

    QLineEdit {
        background-color: #3c3c3c;
        border: 2px solid #555555;
        border-radius: 4px;
        padding: 5px;
        color: #ffffff;
        selection-background-color: #0078d4;
    }

    QLineEdit:focus {
        border-color: #0078d4;
    }

    QLineEdit:read-only {
        background-color: #404040;
        color: #cccccc;
    }

    QComboBox {
        background-color: #3c3c3c;
        border: 2px solid #555555;
        border-radius: 4px;
        padding: 5px;
        color: #ffffff;
        min-width: 200px;
    }

    QComboBox:hover {
        border-color: #0078d4;
    }

    QComboBox::drop-down {
        subcontrol-origin: padding;
        subcontrol-position: top right;
        width: 20px;
        border-left: 1px solid #555555;
        background-color: #3c3c3c;
    }

    QComboBox::down-arrow {
        image: none;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #ffffff;
        margin: 5px;
    }

    QComboBox QAbstractItemView {
        background-color: #3c3c3c;
        border: 1px solid #555555;
        selection-background-color: #0078d4;
        color: #ffffff;
        min-width: 400px;
        font-family: 'Consolas', 'Courier New', monospace;
    }

    QPushButton {
        background-color: #0078d4;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        color: #ffffff;
        font-weight: bold;
    }

    QPushButton:hover {
        background-color: #106ebe;
    }

    QPushButton:pressed {
        background-color: #005a9e;
    }

    QPushButton:disabled {
        background-color: #555555;
        color: #888888;
    }

    QTableWidget {
        background-color: #3c3c3c;
        alternate-background-color: #404040;
        gridline-color: #555555;
        border: 1px solid #555555;
        selection-background-color: #0078d4;
        color: #ffffff;
    }

    QTableWidget::item {
        padding: 5px;
        border: none;
    }

    QTableWidget::item:selected {
        background-color: #0078d4;
        color: #ffffff;
    }

    QHeaderView::section {
        background-color: #404040;
        color: #ffffff;
        padding: 5px;
        border: 1px solid #555555;
        font-weight: bold;
    }

    QHeaderView::section:hover {
        background-color: #4a4a4a;
    }

    QScrollBar:vertical {
        background-color: #3c3c3c;
        width: 12px;
        border: none;
    }

    QScrollBar::handle:vertical {
        background-color: #555555;
        border-radius: 6px;
        min-height: 20px;
    }

    QScrollBar::handle:vertical:hover {
        background-color: #666666;
    }

    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
        height: 0px;
    }

    QScrollBar:horizontal {
        background-color: #3c3c3c;
        height: 12px;
        border: none;
    }

    QScrollBar::handle:horizontal {
        background-color: #555555;
        border-radius: 6px;
        min-width: 20px;
    }

    QScrollBar::handle:horizontal:hover {
        background-color: #666666;
    }

    QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
        width: 0px;
    }

    QMessageBox {
        background-color: #2b2b2b;
        color: #ffffff;
    }

    QMessageBox QPushButton {
        min-width: 80px;
        padding: 6px 12px;
    }
    """

class DatabaseWorker(QThread):
    """Worker thread for database operations to prevent UI freezing"""
    data_ready = pyqtSignal(pd.DataFrame)
    error_occurred = pyqtSignal(str)

    def __init__(self, query, params=None):
        super().__init__()
        self.query = query
        self.params = params
        self._is_running = False

    def run(self):
        self._is_running = True
        try:
            if not self._is_running:  # Check if we should stop
                return
            db = DatabaseBridge()
            df = db.query_to_dataframe(self.query, self.params)
            if self._is_running:  # Only emit if still running
                self.data_ready.emit(df)
        except Exception as e:
            if self._is_running:  # Only emit if still running
                self.error_occurred.emit(str(e))
        finally:
            self._is_running = False

    def stop(self):
        """Stop the worker thread gracefully"""
        self._is_running = False
        self.quit()
        self.wait(3000)  # Wait up to 3 seconds for thread to finish

class SalesDataApp(QWidget):
    def __init__(self):
        super().__init__()
        self.db = DatabaseBridge()
        self.worker = None
        self.all_customers = []  # Store all customer codes for filtering
        self.customer_data = {}  # Store customer code -> description mapping
        self.customer_display_items = []  # Store formatted display items (code - description)

        # Timer for debounced search
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_customer_filter)

        # Timer for debounced recent items loading
        self.recent_items_timer = QTimer()
        self.recent_items_timer.setSingleShot(True)
        self.recent_items_timer.timeout.connect(self.load_recent_items)

        # Worker threads
        self.worker = None
        self.recent_items_worker = None
        self.initUI()
        self.populate_dropdowns()
        # Initialize recent items table as empty
        self.clear_recent_items()
        # Initialize load items button as disabled
        self.load_items_button.setEnabled(False)
        # Set initial focus to search field
        QTimer.singleShot(100, lambda: self.customer_search.setFocus())

    def initUI(self):
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)

        # Top section with title and reset button
        top_layout = QHBoxLayout()

        # Title
        title_label = QLabel("BWH Price Tool - Customer & Item Selection")
        title_label.setStyleSheet("font-weight: bold; font-size: 12pt; color: #ffffff;")

        # Reset button
        self.reset_button = QPushButton("🔄 Reset Filters")
        self.reset_button.setMaximumWidth(120)
        self.reset_button.setMinimumHeight(35)
        self.reset_button.clicked.connect(self.reset_filters)
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                border: none;
                border-radius: 4px;
                padding: 8px 12px;
                color: #ffffff;
                font-weight: bold;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)

        top_layout.addWidget(title_label)
        top_layout.addStretch()  # Push reset button to the right
        top_layout.addWidget(self.reset_button)

        layout.addLayout(top_layout)

        # Customer selection with search
        customer_label = QLabel("Customer Selection:")
        customer_label.setStyleSheet("font-weight: bold; font-size: 10pt;")

        # Customer search and dropdown layout
        customer_layout = QHBoxLayout()

        # Customer search field
        self.customer_search = QLineEdit()
        self.customer_search.setPlaceholderText("Type customer code or description (6+ chars to show dropdown)...")
        self.customer_search.setMinimumHeight(30)
        self.customer_search.setMaximumWidth(200)  # 12 characters wide approximately
        self.customer_search.textChanged.connect(self.on_search_text_changed)
        self.customer_search.returnPressed.connect(self.on_search_enter_pressed)
        # Override key press events to handle arrow keys
        self.customer_search.keyPressEvent = self.search_key_press_event
        # Ensure focus stays in search field
        self.customer_search.focusInEvent = self.search_focus_in_event

        # Customer dropdown - completely non-focusable, selection only
        self.customer_combo = QComboBox()
        self.customer_combo.setMinimumHeight(30)
        self.customer_combo.setMinimumWidth(350)  # 15 characters wider approximately
        self.customer_combo.setEditable(False)
        self.customer_combo.setMaxVisibleItems(15)  # Show more items in dropdown
        self.customer_combo.setInsertPolicy(QComboBox.NoInsert)
        self.customer_combo.setFocusPolicy(Qt.NoFocus)  # Prevent combo from taking focus
        self.customer_combo.currentTextChanged.connect(self.update_customer_description)
        self.customer_combo.activated.connect(self.on_customer_selected)  # When user clicks/selects

        # Make dropdown completely non-interactive for keyboard
        self.customer_combo.view().setFocusPolicy(Qt.NoFocus)
        self.customer_combo.setStyleSheet(self.customer_combo.styleSheet() + """
            QComboBox:focus { border-color: #555555; }
            QComboBox::drop-down:focus { background-color: #3c3c3c; }
        """)

        # Customer description display
        self.customer_desc_display = QLineEdit()
        self.customer_desc_display.setReadOnly(True)
        self.customer_desc_display.setMinimumHeight(30)
        self.customer_desc_display.setPlaceholderText("Customer description will appear here")
        self.customer_desc_display.setStyleSheet("background-color: #404040; color: #cccccc;")

        search_label = QLabel("Search:")
        search_label.setStyleSheet("font-size: 9pt; color: #cccccc;")
        select_label = QLabel("Code:")
        select_label.setStyleSheet("font-size: 9pt; color: #cccccc;")
        desc_label = QLabel("Selected Customer:")
        desc_label.setStyleSheet("font-size: 9pt; color: #cccccc;")

        # Load Item Sales button
        self.load_items_button = QPushButton("📋 Load Item Sales")
        self.load_items_button.setMaximumWidth(140)
        self.load_items_button.setMinimumHeight(30)
        self.load_items_button.clicked.connect(self.manual_load_recent_items)
        self.load_items_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                border: none;
                border-radius: 4px;
                padding: 6px 10px;
                color: #ffffff;
                font-weight: bold;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        customer_layout.addWidget(search_label)
        customer_layout.addWidget(self.customer_search, 2)  # Give search field more space
        customer_layout.addWidget(select_label)
        customer_layout.addWidget(self.customer_combo, 1)   # Customer code dropdown
        customer_layout.addWidget(desc_label)
        customer_layout.addWidget(self.customer_desc_display, 2)  # Description display
        customer_layout.addWidget(self.load_items_button)  # Load items button

        # Item selection
        item_label = QLabel("Item Code:")
        item_label.setStyleSheet("font-weight: bold; font-size: 10pt;")
        self.item_combo = QComboBox()
        self.item_combo.setMinimumHeight(30)

        # Recent items for selected customer
        recent_items_label = QLabel("Recent Items Sales for Selected Customer:")
        recent_items_label.setStyleSheet("font-weight: bold; font-size: 10pt; color: #cccccc;")

        # Table for recent items - horizontal layout with groups of 5
        self.recent_items_table = QTableWidget()
        self.recent_items_table.setRowCount(5)  # Fixed 5 rows
        # Remove height restrictions to show full table
        self.recent_items_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.recent_items_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        # 9 columns: 3 groups of 3 (Code + Description + Date)
        self.recent_items_table.setColumnCount(9)
        self.recent_items_table.setHorizontalHeaderLabels([
            "Code", "Description", "Date",
            "Code", "Description", "Date",
            "Code", "Description", "Date"
        ])
        self.recent_items_table.setAlternatingRowColors(True)
        self.recent_items_table.setSortingEnabled(False)
        self.recent_items_table.setSelectionBehavior(QTableWidget.SelectItems)
        self.recent_items_table.setSelectionMode(QTableWidget.SingleSelection)
        # Connect double-click to select item
        self.recent_items_table.cellDoubleClicked.connect(self.on_recent_item_selected)

        # GP input section
        gp_label = QLabel("New GP% (Optional):")
        gp_label.setStyleSheet("font-weight: bold; font-size: 10pt;")
        self.new_gp_input = QLineEdit()
        self.new_gp_input.setPlaceholderText("Enter NEW GP%")
        self.new_gp_input.setMinimumHeight(30)

        # Current GP display
        self.new_gp_label = QLabel("Current Average GP%:")
        self.new_gp_label.setStyleSheet("font-weight: bold; font-size: 10pt;")
        self.new_gp_display = QLineEdit()
        self.new_gp_display.setReadOnly(True)
        self.new_gp_display.setMinimumHeight(30)
        self.new_gp_display.setPlaceholderText("Will show after fetching data")

        # Fetch button
        self.fetch_button = QPushButton("🔍 Fetch Sales Data")
        self.fetch_button.clicked.connect(self.fetch_data)
        self.fetch_button.setMinimumHeight(40)
        self.fetch_button.setStyleSheet("font-size: 11pt; font-weight: bold;")

        # Data table
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)
        # Allow horizontal scrolling if needed but minimize vertical scrolling
        self.table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Add widgets to layout
        layout.addWidget(customer_label)
        layout.addLayout(customer_layout)
        layout.addWidget(recent_items_label)
        layout.addWidget(self.recent_items_table)
        layout.addWidget(item_label)
        layout.addWidget(self.item_combo)
        layout.addWidget(gp_label)
        layout.addWidget(self.new_gp_input)
        layout.addWidget(self.new_gp_label)
        layout.addWidget(self.new_gp_display)
        layout.addWidget(self.fetch_button)
        layout.addWidget(self.table)

        self.setLayout(layout)

    def populate_dropdowns(self):
        """Populate dropdown menus with customer and item codes"""
        try:
            # Load customers from CustomerMaster table
            customers_df = self.db.query_to_dataframe(
                "SELECT CustomerCode, CustomerDesc FROM CustomerMaster ORDER BY CustomerCode"
            )
            if not customers_df.empty:
                self.all_customers = customers_df['CustomerCode'].tolist()
                # Create mapping of customer code to description
                self.customer_data = dict(zip(customers_df['CustomerCode'], customers_df['CustomerDesc']))

                # Create display items that show both code and description
                self.customer_display_items = []
                for _, row in customers_df.iterrows():
                    code = row['CustomerCode']
                    desc = row['CustomerDesc'] if pd.notna(row['CustomerDesc']) else ""
                    display_text = f"{code} - {desc}" if desc else code
                    self.customer_display_items.append(display_text)

                self.customer_combo.addItems(self.customer_display_items)

            # Load items
            items_df = self.db.query_to_dataframe("SELECT DISTINCT ItemCode FROM HistoryLines ORDER BY ItemCode")
            if not items_df.empty:
                self.item_combo.addItems(items_df['ItemCode'].tolist())

        except Exception as e:
            QMessageBox.critical(self, "Database Error", f"Failed to load dropdown data: {str(e)}")

    def on_search_text_changed(self):
        """Handle search text changes with debouncing"""
        # Stop the previous timer
        self.search_timer.stop()
        # Start a new timer with 100ms delay for more responsive search
        self.search_timer.start(100)

    def perform_customer_filter(self):
        """Perform the actual customer filtering"""
        search_text = self.customer_search.text().upper().strip()

        # Store current selection to try to preserve it
        current_selection = self.customer_combo.currentText()

        # Clear current items
        self.customer_combo.clear()

        if not search_text:
            # If search is empty, show all customers but don't show dropdown
            self.customer_combo.addItems(self.customer_display_items)
            self.customer_combo.hidePopup()
        elif len(search_text) < 6:
            # For searches less than 6 characters, filter but don't show dropdown
            filtered_items = []
            for display_item in self.customer_display_items:
                if search_text in display_item.upper():
                    filtered_items.append(display_item)

            self.customer_combo.addItems(filtered_items)
            self.customer_combo.hidePopup()  # Keep dropdown hidden
        else:
            # For 6+ characters, filter and show dropdown
            filtered_items = []
            for display_item in self.customer_display_items:
                if search_text in display_item.upper():
                    filtered_items.append(display_item)

            self.customer_combo.addItems(filtered_items)

            # Show dropdown if there are filtered results
            if filtered_items:
                if not self.customer_combo.view().isVisible():
                    self.customer_combo.showPopup()
            else:
                self.customer_combo.hidePopup()

        # Try to restore previous selection if it's still in the filtered list
        if current_selection:
            index = self.customer_combo.findText(current_selection)
            if index >= 0:
                self.customer_combo.setCurrentIndex(index)

    def search_key_press_event(self, event):
        """Handle key press events in the search field"""
        if event.key() == Qt.Key_Down:
            # Arrow down: move to next item in dropdown
            if self.customer_combo.count() > 0:
                current_index = self.customer_combo.currentIndex()
                if current_index < self.customer_combo.count() - 1:
                    self.customer_combo.setCurrentIndex(current_index + 1)
                else:
                    self.customer_combo.setCurrentIndex(0)  # Wrap to first
                if not self.customer_combo.view().isVisible():
                    self.customer_combo.showPopup()
        elif event.key() == Qt.Key_Up:
            # Arrow up: move to previous item in dropdown
            if self.customer_combo.count() > 0:
                current_index = self.customer_combo.currentIndex()
                if current_index > 0:
                    self.customer_combo.setCurrentIndex(current_index - 1)
                else:
                    self.customer_combo.setCurrentIndex(self.customer_combo.count() - 1)  # Wrap to last
                if not self.customer_combo.view().isVisible():
                    self.customer_combo.showPopup()
        elif event.key() == Qt.Key_Escape:
            # Escape: hide dropdown and clear search
            self.customer_combo.hidePopup()
            self.customer_search.clear()
        else:
            # For all other keys, use default behavior
            QLineEdit.keyPressEvent(self.customer_search, event)

    def on_search_enter_pressed(self):
        """Handle Enter key press in search field"""
        # If there are items in the dropdown, select the current one
        if self.customer_combo.count() > 0:
            current_index = self.customer_combo.currentIndex()
            if current_index >= 0:
                # Trigger the selection
                self.on_customer_selected(current_index)
            self.customer_combo.hidePopup()

    def on_customer_selected(self, index=None):
        """Handle when user explicitly selects a customer"""
        # This is called when user clicks on dropdown item
        # Update description
        self.update_customer_description()
        # Return focus to search field after a brief delay
        QTimer.singleShot(10, lambda: self.customer_search.setFocus())

    def search_focus_in_event(self, event):
        """Handle focus in event for search field"""
        QLineEdit.focusInEvent(self.customer_search, event)
        # Only show dropdown if search text is 6+ characters
        search_text = self.customer_search.text().strip()
        if len(search_text) >= 6 and self.customer_combo.count() > 0:
            if not self.customer_combo.view().isVisible():
                self.customer_combo.showPopup()

    def reset_filters(self):
        """Reset all filters and selections to initial state"""
        # Stop any running timers
        self.search_timer.stop()
        self.recent_items_timer.stop()

        # Stop any running worker threads
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.worker = None

        if self.recent_items_worker and self.recent_items_worker.isRunning():
            self.recent_items_worker.stop()
            self.recent_items_worker = None

        # Clear search field
        self.customer_search.clear()

        # Reset customer dropdown to show all customers
        self.customer_combo.clear()
        if self.customer_display_items:
            self.customer_combo.addItems(self.customer_display_items)

        # Clear customer description
        self.customer_desc_display.clear()

        # Reset item dropdown selection
        if self.item_combo.count() > 0:
            self.item_combo.setCurrentIndex(0)

        # Clear GP input and display
        self.new_gp_input.clear()
        self.new_gp_display.clear()

        # Clear recent items table
        self.clear_recent_items()

        # Reset load items button
        self.load_items_button.setEnabled(False)
        self.load_items_button.setText("📋 Load Item Sales")

        # Clear main data table
        self.table.setRowCount(0)
        self.table.setColumnCount(0)

        # Reset fetch button
        self.fetch_button.setEnabled(True)
        self.fetch_button.setText("🔍 Fetch Sales Data")

        # Set focus back to search field
        QTimer.singleShot(100, lambda: self.customer_search.setFocus())

    def manual_load_recent_items(self):
        """Manually load recent items when user clicks the button"""
        customer_code = self.get_selected_customer_code()
        if not customer_code:
            QMessageBox.warning(self, "No Customer Selected", "Please select a customer first.")
            return

        # Disable button during loading
        self.load_items_button.setEnabled(False)
        self.load_items_button.setText("Loading...")

        # Load recent items
        self.load_recent_items()

    def load_recent_items(self):
        """Load recent items for the selected customer"""
        customer_code = self.get_selected_customer_code()
        if not customer_code:
            self.clear_recent_items()
            return

        # Stop any existing recent items worker
        if self.recent_items_worker and self.recent_items_worker.isRunning():
            self.recent_items_worker.stop()
            self.recent_items_worker = None

        # Show loading indicator
        self.show_recent_items_loading()

        try:
            # Query to get last 15 items sold to this customer (for 3 groups of 5)
            query = """
            SELECT TOP 15 h.ItemCode, h.Description, MAX(h.DDate) as LastSaleDate
            FROM HistoryLines h
            WHERE h.CustomerCode = ? AND h.SearchType = '4'
            GROUP BY h.ItemCode, h.Description
            ORDER BY MAX(h.DDate) DESC
            """

            # Use worker thread for database query
            self.recent_items_worker = DatabaseWorker(query, [customer_code])
            self.recent_items_worker.data_ready.connect(self.display_recent_items)
            self.recent_items_worker.error_occurred.connect(self.handle_recent_items_error)
            self.recent_items_worker.finished.connect(self.cleanup_recent_items_worker)
            self.recent_items_worker.start()

        except Exception as e:
            print(f"Error loading recent items: {e}")
            self.clear_recent_items()

    def show_recent_items_loading(self):
        """Show loading indicator in recent items table"""
        # Clear all cells first
        self.clear_recent_items()
        # Show loading message in the first cell
        loading_item = QTableWidgetItem("Loading recent items...")
        self.recent_items_table.setItem(0, 0, loading_item)

    def cleanup_recent_items_worker(self):
        """Clean up the recent items worker thread"""
        # Reset the load items button
        self.load_items_button.setEnabled(True)
        self.load_items_button.setText("📋 Load Item Sales")

        if self.recent_items_worker:
            # Disconnect all signals to prevent issues
            try:
                self.recent_items_worker.data_ready.disconnect()
                self.recent_items_worker.error_occurred.disconnect()
                self.recent_items_worker.finished.disconnect()
            except:
                pass  # Ignore if already disconnected

            self.recent_items_worker.deleteLater()
            self.recent_items_worker = None

    def display_recent_items(self, data):
        """Display recent items in the table with horizontal layout"""
        if data.empty:
            self.clear_recent_items()
            return

        # Clear all cells first
        for row in range(5):
            for col in range(9):
                self.recent_items_table.setItem(row, col, QTableWidgetItem(""))

        # Fill table with data in horizontal groups
        for i, row_data in data.iterrows():
            if i >= 15:  # Limit to 15 items (3 groups of 5)
                break

            # Determine which group (0-2) and position within group (0-4)
            group = i // 5  # 0 for items 1-5, 1 for items 6-10, 2 for items 11-15
            position = i % 5  # Position within the group (0-4)

            # Calculate column offsets for each group (3 columns per group: Code, Description, Date)
            col_offset = group * 3

            # Item Code
            item_code = QTableWidgetItem(str(row_data['ItemCode']))
            self.recent_items_table.setItem(position, col_offset, item_code)

            # Description
            description = QTableWidgetItem(str(row_data['Description']))
            self.recent_items_table.setItem(position, col_offset + 1, description)

            # Last Sale Date
            last_sale = QTableWidgetItem(str(row_data['LastSaleDate']))
            self.recent_items_table.setItem(position, col_offset + 2, last_sale)

        # Set dynamic column widths with maximum limits
        self.set_dynamic_column_widths()

    def set_dynamic_column_widths(self):
        """Set dynamic column widths with maximum limits"""
        # Resize to content first
        self.recent_items_table.resizeColumnsToContents()

        # Set maximum widths to prevent excessive spacing
        for col in range(9):
            current_width = self.recent_items_table.columnWidth(col)
            col_type = col % 3  # 0=code, 1=description, 2=date

            if col_type == 0:  # Code column
                max_width = 80
            elif col_type == 1:  # Description column
                max_width = 200
            else:  # Date column
                max_width = 100

            # Set the smaller of current width or max width
            self.recent_items_table.setColumnWidth(col, min(current_width, max_width))

    def clear_recent_items(self):
        """Clear the recent items table"""
        # Clear all cells but keep the structure
        for row in range(5):
            for col in range(9):
                self.recent_items_table.setItem(row, col, QTableWidgetItem(""))

    def handle_recent_items_error(self, error_message):
        """Handle errors when loading recent items"""
        print(f"Error loading recent items: {error_message}")
        self.clear_recent_items()
        # Reset the load items button
        self.load_items_button.setEnabled(True)
        self.load_items_button.setText("📋 Load Item Sales")
        QMessageBox.warning(self, "Error", f"Failed to load recent items: {error_message}")

    def on_recent_item_selected(self, row, column):
        """Handle when user double-clicks on a recent item"""
        # Determine which group was clicked based on column (3 columns per group: Code, Description, Date)
        col_type = column % 3

        if col_type == 0:  # Item code column
            item_code_item = self.recent_items_table.item(row, column)
        elif col_type == 1:  # Description column - get the item code from previous column
            item_code_item = self.recent_items_table.item(row, column - 1)
        else:  # Date column - get the item code from 2 columns back
            item_code_item = self.recent_items_table.item(row, column - 2)

        if item_code_item and item_code_item.text().strip():
            item_code = item_code_item.text()

            # Find and select this item in the item combo
            index = self.item_combo.findText(item_code)
            if index >= 0:
                self.item_combo.setCurrentIndex(index)
            else:
                # If item not found in combo, add it temporarily
                self.item_combo.addItem(item_code)
                self.item_combo.setCurrentText(item_code)

    def update_customer_description(self):
        """Update the customer description display when selection changes"""
        selected_display_text = self.customer_combo.currentText()
        if selected_display_text:
            # Extract customer code from display text (format: "CODE - DESCRIPTION")
            customer_code = selected_display_text.split(" - ")[0] if " - " in selected_display_text else selected_display_text
            if customer_code in self.customer_data:
                description = self.customer_data[customer_code]
                self.customer_desc_display.setText(description)
                # Enable the load items button
                self.load_items_button.setEnabled(True)
            else:
                self.customer_desc_display.clear()
                self.load_items_button.setEnabled(False)
        else:
            self.customer_desc_display.clear()
            self.load_items_button.setEnabled(False)

    def get_selected_customer_code(self):
        """Get the customer code from the currently selected item"""
        selected_display_text = self.customer_combo.currentText()
        if selected_display_text:
            # Extract customer code from display text (format: "CODE - DESCRIPTION")
            return selected_display_text.split(" - ")[0] if " - " in selected_display_text else selected_display_text
        return ""

    def fetch_data(self):
        """Fetch sales data using worker thread"""
        customer_code = self.get_selected_customer_code()
        item_code = self.item_combo.currentText()

        if not customer_code or not item_code:
            QMessageBox.warning(self, "Selection Required", "Please select both customer and item codes.")
            return

        # Stop any existing worker
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.worker = None

        # Disable button during fetch
        self.fetch_button.setEnabled(False)
        self.fetch_button.setText("Loading...")

        query = """
        SELECT TOP 10 CustomerCode, ItemCode, SalesmanCode, DDate,
               Description, CostPrice, Qty, UnitPrice
        FROM HistoryLines
        WHERE SearchType = '4' AND CustomerCode = ? AND ItemCode = ?
        ORDER BY DDate DESC
        """

        # Create and start worker thread
        self.worker = DatabaseWorker(query, [customer_code, item_code])
        self.worker.data_ready.connect(self.display_sales_data)
        self.worker.error_occurred.connect(self.show_error)
        self.worker.finished.connect(self.reset_fetch_button)
        self.worker.finished.connect(self.cleanup_main_worker)
        self.worker.start()

    def cleanup_main_worker(self):
        """Clean up the main worker thread"""
        if self.worker:
            # Disconnect all signals to prevent issues
            try:
                self.worker.data_ready.disconnect()
                self.worker.error_occurred.disconnect()
                self.worker.finished.disconnect()
            except:
                pass  # Ignore if already disconnected

            self.worker.deleteLater()
            self.worker = None

    def display_sales_data(self, data):
        """Display the fetched sales data in the table"""
        if data.empty:
            QMessageBox.warning(self, "No Data", "No records found for the selected criteria.")
            return

        # Calculate GP and GP%
        data['GP'] = data['UnitPrice'] - data['CostPrice']
        data['GP%'] = (data['GP'] / data['UnitPrice']) * 100

        # Update GP% display
        self.new_gp_display.setText(f"{data['GP%'].mean():.2f}%")

        # Set up table
        self.table.setRowCount(len(data))
        self.table.setColumnCount(len(data.columns))
        self.table.setHorizontalHeaderLabels(data.columns.tolist())

        # Fill table with data
        for i in range(len(data)):
            for j in range(len(data.columns)):
                self.table.setItem(i, j, QTableWidgetItem(str(data.iat[i, j])))

        # Resize columns to content
        self.table.resizeColumnsToContents()

    def show_error(self, error_message):
        """Show error message"""
        QMessageBox.critical(self, "Database Error", error_message)

    def reset_fetch_button(self):
        """Reset fetch button after operation completes"""
        self.fetch_button.setEnabled(True)
        self.fetch_button.setText("Fetch Sales Data")

    def closeEvent(self, event):
        """Clean up when closing the application"""
        # Stop all timers
        self.search_timer.stop()
        self.recent_items_timer.stop()

        # Clean up main worker thread
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.worker = None

        # Clean up recent items worker thread
        if self.recent_items_worker and self.recent_items_worker.isRunning():
            self.recent_items_worker.stop()
            self.recent_items_worker = None

        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Apply dark theme
    app.setStyleSheet(get_dark_stylesheet())

    window = SalesDataApp()
    window.setWindowTitle("BWH Price Tool - Sales Data Query")
    window.showMaximized()  # Make the app full screen
    sys.exit(app.exec_())