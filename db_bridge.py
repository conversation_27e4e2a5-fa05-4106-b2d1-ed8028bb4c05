import subprocess
import json
import pandas as pd
from typing import List, Dict, Any, Optional

class DatabaseBridge:
    """
    Bridge class to connect 64-bit Python application to 32-bit database
    using subprocess communication
    """
    
    def __init__(self, python32_path: str = r'C:\py\py31132\python.exe', 
                 script_path: str = r'C:\Software\BWH_PRICE_TOOL\connect32.py'):
        self.python32_path = python32_path
        self.script_path = script_path
    
    def execute_query(self, query: str, params: Optional[List] = None) -> Dict[str, Any]:
        """
        Execute a SQL query using the 32-bit Python subprocess
        
        Args:
            query: SQL query string
            params: Optional parameters for the query
            
        Returns:
            Dictionary with success status and data/error
        """
        try:
            # Prepare command arguments
            cmd_args = [self.python32_path, self.script_path, query]
            if params:
                cmd_args.append(json.dumps(params, default=str))
            
            # Execute subprocess
            result = subprocess.run(
                cmd_args,
                capture_output=True,
                text=True,
                timeout=30  # 30 second timeout
            )
            
            if result.returncode != 0:
                return {
                    "success": False, 
                    "error": f"Subprocess error: {result.stderr}"
                }
            
            # Parse JSON response
            response = json.loads(result.stdout)
            return response
            
        except subprocess.TimeoutExpired:
            return {"success": False, "error": "Query timeout"}
        except json.JSONDecodeError as e:
            return {"success": False, "error": f"JSON decode error: {e}"}
        except Exception as e:
            return {"success": False, "error": f"Unexpected error: {e}"}
    
    def query_to_dataframe(self, query: str, params: Optional[List] = None) -> pd.DataFrame:
        """
        Execute query and return results as pandas DataFrame
        
        Args:
            query: SQL query string
            params: Optional parameters for the query
            
        Returns:
            pandas DataFrame with query results
        """
        result = self.execute_query(query, params)
        
        if not result["success"]:
            raise Exception(f"Database query failed: {result['error']}")
        
        # Convert to DataFrame
        if result["data"]:
            return pd.DataFrame(result["data"])
        else:
            return pd.DataFrame()  # Empty DataFrame
    
    def test_connection(self) -> bool:
        """Test if the database connection is working"""
        result = self.execute_query("SELECT 1 as test")
        return result["success"]

# Example usage functions
def get_ledger_data(limit: int = 10) -> pd.DataFrame:
    """Get ledger master data as DataFrame"""
    db = DatabaseBridge()
    query = f"SELECT TOP {limit} * FROM LedgerMaster"
    return db.query_to_dataframe(query)

def search_ledger(search_term: str) -> pd.DataFrame:
    """Search ledger master by name or code"""
    db = DatabaseBridge()
    query = """
    SELECT * FROM LedgerMaster 
    WHERE LedgerName LIKE ? OR LedgerCode LIKE ?
    """
    params = [f"%{search_term}%", f"%{search_term}%"]
    return db.query_to_dataframe(query, params)

if __name__ == "__main__":
    # Test the bridge
    db = DatabaseBridge()
    
    print("Testing connection...")
    if db.test_connection():
        print("✓ Connection successful!")
        
        print("\nFetching sample data...")
        df = get_ledger_data(5)
        print(df)
        
    else:
        print("✗ Connection failed!")
