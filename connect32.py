import pyodbc as pdb
import sys
import json

def execute_query(query, params=None):
    """Execute a query and return results as JSON"""
    try:
        # Establish the connection
        conn = pdb.connect("dsn=BWH25;uid=marietjie;pwd=renier")
        cursor = conn.cursor()

        # Execute the query
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)

        # Fetch results
        if query.strip().upper().startswith('SELECT'):
            # Get column names
            columns = [column[0] for column in cursor.description]
            # Fetch all rows
            rows = cursor.fetchall()
            # Convert to list of dictionaries
            result = [dict(zip(columns, row)) for row in rows]
        else:
            # For non-SELECT queries, return affected row count
            result = {"affected_rows": cursor.rowcount}
            conn.commit()

        cursor.close()
        conn.close()

        return {"success": True, "data": result}

    except pdb.Error as e:
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Query passed as command line argument
        query = sys.argv[1]
        params = json.loads(sys.argv[2]) if len(sys.argv) > 2 else None
        result = execute_query(query, params)
        print(json.dumps(result, default=str))
    else:
        # Interactive mode for testing
        query = "SELECT TOP 10 * FROM LedgerMaster"
        result = execute_query(query)
        if result["success"]:
            for row in result["data"]:
                print(row)
        else:
            print("Error:", result["error"])
